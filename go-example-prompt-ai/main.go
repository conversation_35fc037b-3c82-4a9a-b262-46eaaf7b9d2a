package main

import (
	"fmt"
	"net/http"

	"demo/api"

	"github.com/danielgtaylor/huma/v2"
	"github.com/danielgtaylor/huma/v2/adapters/humago"
	"github.com/danielgtaylor/huma/v2/humacli"

	_ "github.com/danielgtaylor/huma/v2/formats/cbor"
)

// Options for the CLI.
type Options struct {
	Port int `help:"Port to listen on" short:"p" default:"8888"`
}

func main() {
	// Create a CLI app which takes a port option.
	cli := humacli.New(func(hooks humacli.Hooks, options *Options) {
		// Create a new mux & API using humago
		mux := http.NewServeMux()
		humaAPI := humago.NewWithPrefix(mux, "/api", huma.DefaultConfig("My API", "1.0.0"))
		api.Register(humaAPI)

		// Tell the CLI how to start your server.
		hooks.OnStart(func() {
			http.ListenAndServe(fmt.Sprintf(":%d", options.Port), mux)
		})
	})

	// Run the CLI. When passed no commands, it starts the server.
	cli.Run()
}
